<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Todo App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>My Todo App</h1>
        </header>
        
        <div class="todo-input-section">
            <input type="text" id="todoInput" placeholder="Add a new task..." maxlength="100">
            <button id="addBtn">Add Task</button>
        </div>
        
        <div class="filter-section">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="active">Active</button>
            <button class="filter-btn" data-filter="completed">Completed</button>
        </div>
        
        <ul id="todoList" class="todo-list">
            <!-- Todo items will be dynamically added here -->
        </ul>
        
        <div class="todo-footer">
            <span id="todoCount">0 tasks remaining</span>
            <button id="clearCompleted">Clear Completed</button>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
