* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    text-align: center;
    padding: 30px 20px;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.todo-input-section {
    padding: 30px;
    display: flex;
    gap: 15px;
    border-bottom: 1px solid #eee;
}

#todoInput {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s ease;
}

#todoInput:focus {
    border-color: #4facfe;
}

#addBtn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#addBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.filter-section {
    padding: 20px 30px;
    display: flex;
    gap: 10px;
    border-bottom: 1px solid #eee;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.filter-btn.active,
.filter-btn:hover {
    background: #4facfe;
    color: white;
    border-color: #4facfe;
}

.todo-list {
    list-style: none;
    max-height: 400px;
    overflow-y: auto;
}

.todo-item {
    display: flex;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.todo-item:hover {
    background-color: #f8f9fa;
}

.todo-item.completed {
    opacity: 0.6;
}

.todo-item.completed .todo-text {
    text-decoration: line-through;
    color: #888;
}

.todo-checkbox {
    width: 20px;
    height: 20px;
    margin-right: 15px;
    cursor: pointer;
    accent-color: #4facfe;
}

.todo-text {
    flex: 1;
    font-size: 16px;
    color: #333;
    word-break: break-word;
}

.delete-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

.delete-btn:hover {
    background: #ff3742;
}

.todo-footer {
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    color: #666;
    font-size: 14px;
}

#clearCompleted {
    background: #ff4757;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

#clearCompleted:hover {
    background: #ff3742;
}

.empty-state {
    text-align: center;
    padding: 60px 30px;
    color: #999;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: 300;
}

/* Responsive design */
@media (max-width: 480px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .todo-input-section {
        flex-direction: column;
        padding: 20px;
    }
    
    .filter-section {
        padding: 15px 20px;
    }
    
    .todo-item {
        padding: 15px 20px;
    }
    
    .todo-footer {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
